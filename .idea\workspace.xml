<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="76b702cc-2850-472c-9039-935eb1ffa66c" name="Changes" comment="需求广场over">
      <change beforePath="$PROJECT_DIR$/.idea/jarRepositories.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/jarRepositories.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_7_15_43__Changes_.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/target/classes/application-druid.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/target/classes/application-druid.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/target/classes/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/target/classes/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/HaitangTopImageController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniPark.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniPark.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/mapper/HaitangTopImageMapper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/IHaitangTopImageService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/impl/HaitangTopImageServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/HaitangTopImageMapper.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniParkMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniParkMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/HaitangTopImageMapper.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandCategoryMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandCategoryMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandDockingMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandDockingMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniParkMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniParkMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniTechStarMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniTechStarMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/XiqingRoadshowRegistrationMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/XiqingRoadshowRegistrationMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-system/target/classes/mapper/system/SysUserMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-system/target/classes/mapper/system/SysUserMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/park/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/park/index.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$PROJECT_DIR$/../../../maven/maven/apache-maven-3.9.6" />
        <option name="localRepository" value="D:\maven\maven\mvn-repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\maven\maven\apache-maven-3.9.6\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2zInxgJ5QRhFOLKqgYyg52o7LSc" />
  <component name="ProjectViewState">
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.ruoyi [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi-miniapp [clean].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.RuoYiApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/assets/icons/svg&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Global Libraries&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\IntelliJ IDEA 2024.2.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\项目记录（吴龙龙）\tjuhaitang_miniapp\ruoyi-ui\src\assets\icons\svg" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="RuoYi-Vue" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="ruoyi-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.RuoYiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.23726.103" />
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.23726.103" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="76b702cc-2850-472c-9039-935eb1ffa66c" name="Changes" comment="" />
      <created>1751427704689</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751427704689</updated>
      <workItem from="1751427708659" duration="47875000" />
      <workItem from="1751855410839" duration="795000" />
      <workItem from="1751856240516" duration="21000" />
      <workItem from="1751856270005" duration="37696000" />
      <workItem from="1751960768436" duration="415000" />
      <workItem from="1751961217500" duration="63000" />
      <workItem from="1751961297784" duration="142000" />
      <workItem from="1751961476241" duration="77000" />
      <workItem from="1751961563445" duration="581000" />
      <workItem from="1751962169130" duration="8266000" />
      <workItem from="1753781958797" duration="7066000" />
      <workItem from="1753844840065" duration="1413000" />
    </task>
    <task id="LOCAL-00001" summary="项目构建">
      <option name="closed" value="true" />
      <created>1751857168179</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751857168179</updated>
    </task>
    <task id="LOCAL-00002" summary="需求广场over">
      <option name="closed" value="true" />
      <created>1751874137094</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751874137094</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="项目构建" />
    <MESSAGE value="需求广场over" />
    <option name="LAST_COMMIT_MESSAGE" value="需求广场over" />
  </component>
</project>