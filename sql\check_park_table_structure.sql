-- 检查园区表当前结构
-- 数据库: tjuhaitang_miniapp_db

USE tjuhaitang_miniapp_db;

-- 1. 查看当前表结构
DESCRIBE mini_park;

-- 2. 详细查看字段信息
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT,
    ORDINAL_POSITION
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'tjuhaitang_miniapp_db' 
    AND TABLE_NAME = 'mini_park'
ORDER BY 
    ORDINAL_POSITION;

-- 3. 检查需要删除的字段是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('字段 ', COLUMN_NAME, ' 存在')
        ELSE '字段不存在'
    END AS field_status,
    COLUMN_NAME
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'tjuhaitang_miniapp_db' 
    AND TABLE_NAME = 'mini_park'
    AND COLUMN_NAME IN ('park_type', 'area_size', 'contact_phone', 'contact_email', 'address', 'website_url', 'established_date')
GROUP BY COLUMN_NAME

UNION ALL

SELECT 
    '需要检查的字段' AS field_status,
    field_name AS COLUMN_NAME
FROM (
    SELECT 'park_type' AS field_name
    UNION SELECT 'area_size'
    UNION SELECT 'contact_phone'
    UNION SELECT 'contact_email'
    UNION SELECT 'address'
    UNION SELECT 'website_url'
    UNION SELECT 'established_date'
) AS fields
WHERE field_name NOT IN (
    SELECT COLUMN_NAME 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'tjuhaitang_miniapp_db' 
    AND TABLE_NAME = 'mini_park'
);

-- 4. 查看表的创建语句
SHOW CREATE TABLE mini_park;
