-- 诊断园区表结构问题
-- 用于解决 "Unknown column 'address' in 'field list'" 错误

USE tjuhaitang_miniapp_db;

-- 1. 查看当前表结构
SELECT '=== 当前 mini_park 表结构 ===' AS info;
DESCRIBE mini_park;

-- 2. 查看详细字段信息
SELECT '=== 详细字段信息 ===' AS info;
SELECT 
    ORDINAL_POSITION AS '序号',
    COLUMN_NAME AS '字段名',
    DATA_TYPE AS '数据类型',
    IS_NULLABLE AS '可为空',
    COLUMN_DEFAULT AS '默认值',
    COLUMN_COMMENT AS '注释'
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'tjuhaitang_miniapp_db' 
    AND TABLE_NAME = 'mini_park'
ORDER BY 
    ORDINAL_POSITION;

-- 3. 检查问题字段是否存在
SELECT '=== 检查问题字段 ===' AS info;
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('❌ 字段 ', COLUMN_NAME, ' 仍然存在于数据库中')
        ELSE '✅ 字段已删除'
    END AS status,
    COLUMN_NAME AS field_name
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'tjuhaitang_miniapp_db' 
    AND TABLE_NAME = 'mini_park'
    AND COLUMN_NAME IN ('address', 'contact_phone', 'contact_email', 'website_url', 'area_size', 'established_date', 'park_type')
GROUP BY COLUMN_NAME;

-- 4. 显示应该保留的字段
SELECT '=== 应该保留的字段 ===' AS info;
SELECT 
    CASE 
        WHEN COLUMN_NAME IN ('park_id', 'park_name', 'park_code', 'description', 'content', 'cover_image', 'sort_order', 'status', 'create_by', 'create_time', 'update_by', 'update_time', 'remark') 
        THEN '✅ 正确字段'
        ELSE '❌ 多余字段'
    END AS status,
    COLUMN_NAME AS field_name,
    DATA_TYPE AS data_type
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'tjuhaitang_miniapp_db' 
    AND TABLE_NAME = 'mini_park'
ORDER BY 
    ORDINAL_POSITION;

-- 5. 生成正确的SQL语句
SELECT '=== 正确的查询语句 ===' AS info;
SELECT CONCAT(
    'SELECT ',
    GROUP_CONCAT(COLUMN_NAME ORDER BY ORDINAL_POSITION SEPARATOR ', '),
    ' FROM mini_park'
) AS correct_sql
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'tjuhaitang_miniapp_db' 
    AND TABLE_NAME = 'mini_park'
    AND COLUMN_NAME IN ('park_id', 'park_name', 'park_code', 'description', 'content', 'cover_image', 'sort_order', 'status', 'create_by', 'create_time', 'update_by', 'update_time', 'remark');

-- 6. 如果需要删除多余字段，生成删除语句
SELECT '=== 需要删除的字段 ===' AS info;
SELECT 
    CONCAT('ALTER TABLE mini_park DROP COLUMN ', COLUMN_NAME, ';') AS delete_sql
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'tjuhaitang_miniapp_db' 
    AND TABLE_NAME = 'mini_park'
    AND COLUMN_NAME IN ('address', 'contact_phone', 'contact_email', 'website_url', 'area_size', 'established_date', 'park_type');

-- 7. 测试查询（仅查询应该存在的字段）
SELECT '=== 测试查询 ===' AS info;
SELECT COUNT(*) AS record_count FROM mini_park;

-- 8. 显示建议
SELECT '=== 解决建议 ===' AS info;
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'tjuhaitang_miniapp_db' 
            AND TABLE_NAME = 'mini_park'
            AND COLUMN_NAME IN ('address', 'contact_phone', 'contact_email', 'website_url', 'area_size', 'established_date', 'park_type')
        )
        THEN '数据库中仍有多余字段，需要执行删除操作'
        ELSE '数据库字段正确，问题可能在代码缓存或其他地方'
    END AS suggestion;
