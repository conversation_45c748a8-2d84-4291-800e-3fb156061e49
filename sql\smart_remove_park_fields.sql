-- 智能删除园区表多余字段脚本
-- 会先检查字段是否存在，然后安全删除
-- 数据库: tjuhaitang_miniapp_db

USE tjuhaitang_miniapp_db;

-- 设置变量
SET @database_name = 'tjuhaitang_miniapp_db';
SET @table_name = 'mini_park';

-- 1. 首先查看当前表结构
SELECT '=== 当前表结构 ===' AS info;
DESCRIBE mini_park;

-- 2. 检查需要删除的字段
SELECT '=== 检查需要删除的字段 ===' AS info;
SELECT 
    COLUMN_NAME AS '字段名',
    DATA_TYPE AS '数据类型',
    IS_NULLABLE AS '可为空',
    COLUMN_COMMENT AS '注释'
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = @database_name
    AND TABLE_NAME = @table_name
    AND COLUMN_NAME IN ('park_type', 'area_size', 'contact_phone', 'contact_email', 'address', 'website_url', 'established_date');

-- 3. 动态生成删除语句（仅针对存在的字段）
SELECT '=== 生成删除语句 ===' AS info;

-- 检查并删除 park_type 字段
SET @sql = (
    SELECT CASE 
        WHEN COUNT(*) > 0 THEN 'ALTER TABLE mini_park DROP COLUMN park_type;'
        ELSE 'SELECT "字段 park_type 不存在，跳过删除" AS message;'
    END
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = @database_name AND TABLE_NAME = @table_name AND COLUMN_NAME = 'park_type'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并删除 area_size 字段
SET @sql = (
    SELECT CASE 
        WHEN COUNT(*) > 0 THEN 'ALTER TABLE mini_park DROP COLUMN area_size;'
        ELSE 'SELECT "字段 area_size 不存在，跳过删除" AS message;'
    END
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = @database_name AND TABLE_NAME = @table_name AND COLUMN_NAME = 'area_size'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并删除 contact_phone 字段
SET @sql = (
    SELECT CASE 
        WHEN COUNT(*) > 0 THEN 'ALTER TABLE mini_park DROP COLUMN contact_phone;'
        ELSE 'SELECT "字段 contact_phone 不存在，跳过删除" AS message;'
    END
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = @database_name AND TABLE_NAME = @table_name AND COLUMN_NAME = 'contact_phone'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并删除 contact_email 字段
SET @sql = (
    SELECT CASE 
        WHEN COUNT(*) > 0 THEN 'ALTER TABLE mini_park DROP COLUMN contact_email;'
        ELSE 'SELECT "字段 contact_email 不存在，跳过删除" AS message;'
    END
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = @database_name AND TABLE_NAME = @table_name AND COLUMN_NAME = 'contact_email'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并删除 address 字段
SET @sql = (
    SELECT CASE 
        WHEN COUNT(*) > 0 THEN 'ALTER TABLE mini_park DROP COLUMN address;'
        ELSE 'SELECT "字段 address 不存在，跳过删除" AS message;'
    END
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = @database_name AND TABLE_NAME = @table_name AND COLUMN_NAME = 'address'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并删除 website_url 字段
SET @sql = (
    SELECT CASE 
        WHEN COUNT(*) > 0 THEN 'ALTER TABLE mini_park DROP COLUMN website_url;'
        ELSE 'SELECT "字段 website_url 不存在，跳过删除" AS message;'
    END
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = @database_name AND TABLE_NAME = @table_name AND COLUMN_NAME = 'website_url'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并删除 established_date 字段
SET @sql = (
    SELECT CASE 
        WHEN COUNT(*) > 0 THEN 'ALTER TABLE mini_park DROP COLUMN established_date;'
        ELSE 'SELECT "字段 established_date 不存在，跳过删除" AS message;'
    END
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = @database_name AND TABLE_NAME = @table_name AND COLUMN_NAME = 'established_date'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 查看删除后的表结构
SELECT '=== 删除后的表结构 ===' AS info;
DESCRIBE mini_park;

-- 5. 详细验证最终结果
SELECT '=== 最终字段列表 ===' AS info;
SELECT 
    ORDINAL_POSITION AS '序号',
    COLUMN_NAME AS '字段名',
    DATA_TYPE AS '数据类型',
    IS_NULLABLE AS '可为空',
    COLUMN_DEFAULT AS '默认值',
    COLUMN_COMMENT AS '注释'
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = @database_name
    AND TABLE_NAME = @table_name
ORDER BY 
    ORDINAL_POSITION;
