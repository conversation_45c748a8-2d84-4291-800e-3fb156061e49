import java.sql.*;

public class DatabaseTest {
    private static final String URL = "*****************************************************************************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "123456";
    
    public static void main(String[] args) {
        try {
            // 加载MySQL驱动
            Class.forName("com.mysql.cj.jdbc.Driver");
            
            // 建立连接
            Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            System.out.println("数据库连接成功！");
            
            // 查看mini_park表结构
            System.out.println("\n=== 当前mini_park表结构 ===");
            DatabaseMetaData metaData = conn.getMetaData();
            ResultSet columns = metaData.getColumns(null, null, "mini_park", null);
            
            while (columns.next()) {
                String columnName = columns.getString("COLUMN_NAME");
                String dataType = columns.getString("TYPE_NAME");
                String isNullable = columns.getString("IS_NULLABLE");
                System.out.println(columnName + " - " + dataType + " - " + isNullable);
            }
            
            // 检查需要删除的字段是否存在
            String[] fieldsToDelete = {"park_type", "area_size", "contact_phone", "contact_email", "address", "website_url", "established_date"};
            
            System.out.println("\n=== 检查需要删除的字段 ===");
            for (String field : fieldsToDelete) {
                if (columnExists(conn, "mini_park", field)) {
                    System.out.println("字段 " + field + " 存在，需要删除");
                } else {
                    System.out.println("字段 " + field + " 不存在");
                }
            }
            
            // 执行删除字段操作
            System.out.println("\n=== 开始删除多余字段 ===");
            Statement stmt = conn.createStatement();
            
            for (String field : fieldsToDelete) {
                if (columnExists(conn, "mini_park", field)) {
                    try {
                        String sql = "ALTER TABLE mini_park DROP COLUMN " + field;
                        stmt.executeUpdate(sql);
                        System.out.println("成功删除字段: " + field);
                    } catch (SQLException e) {
                        System.out.println("删除字段 " + field + " 失败: " + e.getMessage());
                    }
                }
            }
            
            // 再次查看表结构
            System.out.println("\n=== 删除后的mini_park表结构 ===");
            columns = metaData.getColumns(null, null, "mini_park", null);
            
            while (columns.next()) {
                String columnName = columns.getString("COLUMN_NAME");
                String dataType = columns.getString("TYPE_NAME");
                String isNullable = columns.getString("IS_NULLABLE");
                System.out.println(columnName + " - " + dataType + " - " + isNullable);
            }
            
            stmt.close();
            conn.close();
            System.out.println("\n数据库操作完成！");
            
        } catch (Exception e) {
            System.out.println("数据库操作失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static boolean columnExists(Connection conn, String tableName, String columnName) {
        try {
            DatabaseMetaData metaData = conn.getMetaData();
            ResultSet columns = metaData.getColumns(null, null, tableName, columnName);
            return columns.next();
        } catch (SQLException e) {
            return false;
        }
    }
}
