-- 测试园区表字段删除脚本
-- 数据库: tjuhaitang_miniapp_db

-- 1. 查看当前表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'tjuhaitang_miniapp_db' 
    AND TABLE_NAME = 'mini_park'
ORDER BY 
    ORDINAL_POSITION;

-- 2. 检查需要删除的字段是否存在
SELECT 
    COLUMN_NAME
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'tjuhaitang_miniapp_db' 
    AND TABLE_NAME = 'mini_park'
    AND COLUMN_NAME IN ('park_type', 'area_size', 'contact_phone', 'contact_email', 'address', 'website_url', 'established_date');

-- 3. 如果字段存在，则执行删除操作
-- 注意：这些语句需要根据实际情况逐个执行

-- ALTER TABLE mini_park DROP COLUMN IF EXISTS park_type;
-- ALTER TABLE mini_park DROP COLUMN IF EXISTS area_size;
-- ALTER TABLE mini_park DROP COLUMN IF EXISTS contact_phone;
-- ALTER TABLE mini_park DROP COLUMN IF EXISTS contact_email;
-- ALTER TABLE mini_park DROP COLUMN IF EXISTS address;
-- ALTER TABLE mini_park DROP COLUMN IF EXISTS website_url;
-- ALTER TABLE mini_park DROP COLUMN IF EXISTS established_date;

-- 4. 验证删除结果
-- SELECT 
--     COLUMN_NAME,
--     DATA_TYPE,
--     IS_NULLABLE,
--     COLUMN_DEFAULT,
--     COLUMN_COMMENT
-- FROM 
--     INFORMATION_SCHEMA.COLUMNS 
-- WHERE 
--     TABLE_SCHEMA = 'tjuhaitang_miniapp_db' 
--     AND TABLE_NAME = 'mini_park'
-- ORDER BY 
--     ORDINAL_POSITION;
